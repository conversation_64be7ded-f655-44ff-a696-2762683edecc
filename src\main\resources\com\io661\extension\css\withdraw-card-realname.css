/* 实名认证卡片样式 */
.realname-card {
    -fx-background-color: linear-gradient(to bottom, #2c2c2c, #1e1e1e);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 15, 0, 0, 3);
    -fx-border-color: #404040;
    -fx-border-width: 1px;
    -fx-padding: 20px;
}

/* 标题样式 */
.realname-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #ffffff;
    -fx-padding: 0 0 15 0;
}

/* 关闭按钮样式 */
.close-button {
    -fx-background-color: transparent;
    -fx-text-fill: #ffffff;
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-background-radius: 50%;
    -fx-min-width: 30px;
    -fx-min-height: 30px;
    -fx-max-width: 30px;
    -fx-max-height: 30px;
}

.close-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-text-fill: #ff6b6b;
}

.close-button:pressed {
    -fx-background-color: rgba(255, 255, 255, 0.2);
}

/* 输入框标签样式 */
.input-label {
    -fx-text-fill: #ffffff;
    -fx-font-size: 14px;
    -fx-font-weight: normal;
    -fx-min-width: 80px;
}

/* 输入框样式 */
.realname-input {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: #ffffff;
    -fx-font-size: 14px;
    -fx-background-radius: 6px;
    -fx-border-color: #555555;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-padding: 10px 12px;
    -fx-prompt-text-fill: #888888;
}

.realname-input:focused {
    -fx-border-color: #00CED1;
    -fx-border-width: 2px;
    -fx-background-color: #404040;
}

.realname-input:error {
    -fx-border-color: #ff6b6b;
    -fx-border-width: 2px;
}

/* 提示信息样式 */
.notice-text {
    -fx-text-fill: #FFC107;
    -fx-font-size: 12px;
    -fx-font-style: italic;
    -fx-padding: 5 0;
}

.warning-text {
    -fx-text-fill: #ff6b6b;
    -fx-font-size: 12px;
    -fx-padding: 5 0;
}

/* 确定按钮样式 */
.submit-button {
    -fx-background-color: linear-gradient(to bottom, #00CED1, #00B8CC);
    -fx-text-fill: #ffffff;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-cursor: hand;
    -fx-padding: 12px 40px;
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.3), 8, 0, 0, 2);
    -fx-min-width: 120px;
}

.submit-button:hover {
    -fx-background-color: linear-gradient(to bottom, #00E5E8, #00CED1);
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.5), 12, 0, 0, 3);
}

.submit-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #00B8CC, #00A5B8);
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.2), 4, 0, 0, 1);
}

.submit-button:disabled {
    -fx-background-color: #666666;
    -fx-text-fill: #999999;
    -fx-effect: none;
    -fx-cursor: default;
}

/* 容器样式 */
.realname-container {
    -fx-spacing: 15px;
    -fx-alignment: TOP_CENTER;
}

.input-row {
    -fx-spacing: 10px;
    -fx-alignment: CENTER_LEFT;
    -fx-fill-width: true;
}

.button-row {
    -fx-spacing: 15px;
    -fx-alignment: CENTER;
    -fx-padding: 10 0 0 0;
}

/* 图标样式 */
.security-icon {
    -fx-text-fill: #4CAF50;
    -fx-font-size: 16px;
}

.warning-icon {
    -fx-text-fill: #FFC107;
    -fx-font-size: 14px;
}

/* 分割线样式 */
.separator {
    -fx-stroke: #404040;
    -fx-stroke-width: 1px;
    -fx-opacity: 0.6;
}

/* 信息提示区域 */
.info-section {
    -fx-background-color: rgba(76, 175, 80, 0.1);
    -fx-background-radius: 6px;
    -fx-border-color: #4CAF50;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-padding: 10px;
    -fx-spacing: 5px;
}

.info-section .info-text {
    -fx-text-fill: #4CAF50;
    -fx-font-size: 12px;
}

/* 动画效果 */
.realname-card {
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
}

.realname-card:hover {
    -fx-scale-x: 1.01;
    -fx-scale-y: 1.01;
}

/* 响应式布局 */
.input-row .realname-input {
    -fx-pref-width: 200px;
    -fx-max-width: 300px;
}

/* 验证状态样式 */
.valid-input {
    -fx-border-color: #4CAF50;
    -fx-border-width: 2px;
}

.invalid-input {
    -fx-border-color: #ff6b6b;
    -fx-border-width: 2px;
}
