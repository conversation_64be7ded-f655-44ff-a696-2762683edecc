<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import java.net.URL?>
<AnchorPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="250.0"
            prefWidth="350.0"
            xmlns="http://javafx.com/javafx/21"
            fx:controller="com.io661.extension.controller.UserController">
    <stylesheets>
        <URL value="@../../css/edit-user-info-card.css" />
    </stylesheets>

    <VBox spacing="20" AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0" AnchorPane.bottomAnchor="20.0">
        <!-- 标题和关闭按钮 -->
        <HBox alignment="CENTER_LEFT" spacing="10">
            <Label text="头像预览" styleClass="title-label" />
            <HBox HBox.hgrow="ALWAYS" />
            <Button fx:id="closeAvatarButton" text="×" styleClass="close-button" />
        </HBox>

        <!-- 头像预览区域 -->
        <VBox alignment="CENTER" spacing="15">
            <ImageView fx:id="avatarPreviewImage" fitHeight="100.0" fitWidth="100.0" preserveRatio="true" />
            <Button fx:id="selectFileButton" text="选择文件" styleClass="action-button" prefWidth="100" />
        </VBox>

        <!-- 按钮区域 -->
        <HBox spacing="15" alignment="CENTER">
            <Button fx:id="cancelAvatarButton" text="返回" styleClass="cancel-button" prefWidth="80" />
            <Button fx:id="submitAvatarButton" text="提交" styleClass="submit-button" prefWidth="80" />
        </HBox>
    </VBox>
</AnchorPane>
