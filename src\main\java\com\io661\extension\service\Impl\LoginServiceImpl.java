package com.io661.extension.service.Impl;

import com.google.gson.Gson;
import com.io661.extension.commonResult.CommonShowAlert;
import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.model.Login.LoginReq;
import com.io661.extension.model.Login.SendCodeReq;
import com.io661.extension.service.LoginService;
import javafx.scene.control.Alert;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;


public class LoginServiceImpl implements LoginService {
    private final CommonHttpUrl httpClient;
    private static long lastSendTime = 0; // 记录上次发送验证码的时间
    private static final long SEND_INTERVAL = 60000; // 发送间隔60秒

    public LoginServiceImpl() {
        this.httpClient = new CommonHttpUrl();
    }


    @Override
    // 登录方法
    public String login(String phone, String code) {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            // 手动构建JSON，避免Gson依赖问题
            String jsonBody = String.format("{\"phone\":\"%s\",\"code\":%s,\"remember\":true}",
                phone, code);

            String endpoint = "web/user";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("登录请求接口: " + endpoint);
            System.out.println("登录请求参数: " + jsonBody);
            System.out.println("服务器响应: " + response);

            return response;
        } catch (IOException e) {
            System.err.println("登录失败: " + e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return "";
    }

    @Override
    // 发送验证码
    public boolean sendCode(String phone) {
        try {
            // 检查发送频率限制
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastSendTime < SEND_INTERVAL) {
                long remainingTime = (SEND_INTERVAL - (currentTime - lastSendTime)) / 1000;
                System.err.println("发送验证码过于频繁，请等待 " + remainingTime + " 秒后重试");
                CommonShowAlert.showAlert(Alert.AlertType.WARNING, "提示",
                    "发送验证码过于频繁，请等待 " + remainingTime + " 秒后重试");
                return false;
            }

            String endpoint = "web/sms";

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("User-Agent", "IO661Extension/1.0");
            headers.put("Accept", "application/json, text/plain, */*");
            headers.put("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");

            SendCodeReq request = new SendCodeReq();
            request.setPhone(phone);
            request.setType(0);

            // 手动构建JSON，避免Gson依赖问题
            String jsonBody = String.format("{\"phone\":\"%s\",\"type\":%d}", phone, 0);

            System.out.println("发送验证码请求: " + jsonBody);

            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("发送验证码接口: " + endpoint);
            System.out.println("发送验证码到手机号: " + phone);
            System.out.println("服务器响应: " + response);

            // 解析响应判断是否发送成功
            boolean success = parseResponse(response);

            if (success) {
                // 更新最后发送时间
                lastSendTime = currentTime;
                CommonShowAlert.showAlert(Alert.AlertType.INFORMATION, "成功", "验证码发送成功，请注意查收");
            } else {
                // 显示服务器返回的错误信息
                String errorMsg = extractErrorMessage(response);
                CommonShowAlert.showAlert(Alert.AlertType.ERROR, "发送失败", errorMsg);
            }

            return success;
        } catch (Exception e) {
            System.err.println("发送验证码失败: " + e.getMessage());
            e.printStackTrace();
            CommonShowAlert.showAlert(Alert.AlertType.ERROR, "网络错误",
                "发送验证码失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 解析服务器响应，判断是否发送成功
     */
    private boolean parseResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return false;
        }

        // 简单的JSON解析，检查code字段
        if (response.contains("\"code\":0") || response.contains("\"success\":true")) {
            return true;
        }

        return false;
    }

    /**
     * 从响应中提取错误信息
     */
    private String extractErrorMessage(String response) {
        if (response == null || response.trim().isEmpty()) {
            return "服务器无响应";
        }

        // 简单提取msg字段
        try {
            int msgStart = response.indexOf("\"msg\":\"");
            if (msgStart != -1) {
                msgStart += 7; // "msg":"的长度
                int msgEnd = response.indexOf("\"", msgStart);
                if (msgEnd != -1) {
                    return response.substring(msgStart, msgEnd);
                }
            }
        } catch (Exception e) {
            System.err.println("解析错误信息失败: " + e.getMessage());
        }

        return "验证码发送失败，请稍后重试";
    }
}

