package com.io661.extension.util;

import com.io661.extension.commonURL.CommonHttpUrl;

import java.util.HashMap;
import java.util.Map;

/**
 * HTTPS连接测试工具类
 * 用于测试打包后的应用程序是否能正常进行HTTPS通信
 */
public class HttpsConnectionTest {
    
    /**
     * 测试HTTPS连接
     */
    public static void testHttpsConnection() {
        System.out.println("开始测试HTTPS连接...");
        
        try {
            // 初始化SSL配置
            SSLCertificateManager.initializeSSLContext();
            System.out.println("SSL配置初始化完成");
            
            // 创建HTTP客户端
            CommonHttpUrl httpClient = new CommonHttpUrl();
            
            // 测试GET请求
            System.out.println("测试GET请求...");
            Map<String, String> headers = new HashMap<>();
            headers.put("User-Agent", "IO661Extension/1.0");
            
            try {
                String response = httpClient.doGet("web/test", null, headers);
                System.out.println("GET请求成功，响应: " + response);
            } catch (Exception e) {
                System.err.println("GET请求失败: " + e.getMessage());
                e.printStackTrace();
            }
            
            // 测试POST请求
            System.out.println("测试POST请求...");
            try {
                String jsonBody = "{\"test\": \"data\"}";
                String response = httpClient.doPost("web/test", jsonBody, headers);
                System.out.println("POST请求成功，响应: " + response);
            } catch (Exception e) {
                System.err.println("POST请求失败: " + e.getMessage());
                e.printStackTrace();
            }
            
        } catch (Exception e) {
            System.err.println("HTTPS连接测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("HTTPS连接测试完成");
    }
    
    /**
     * 测试SSL证书配置
     */
    public static void testSSLConfiguration() {
        System.out.println("开始测试SSL证书配置...");
        
        try {
            // 测试SSL上下文
            SSLCertificateManager.initializeSSLContext();
            
            if (SSLCertificateManager.getSSLContext() != null) {
                System.out.println("SSL上下文创建成功");
                System.out.println("SSL协议: " + SSLCertificateManager.getSSLContext().getProtocol());
            } else {
                System.err.println("SSL上下文为null");
            }
            
            if (SSLCertificateManager.getSSLSocketFactory() != null) {
                System.out.println("SSLSocketFactory创建成功");
            } else {
                System.err.println("SSLSocketFactory为null");
            }
            
            if (SSLCertificateManager.getHostnameVerifier() != null) {
                System.out.println("HostnameVerifier创建成功");
            } else {
                System.err.println("HostnameVerifier为null");
            }
            
        } catch (Exception e) {
            System.err.println("SSL证书配置测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("SSL证书配置测试完成");
    }
    
    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        System.out.println("=== HTTPS连接测试工具 ===");
        
        // 设置系统属性
        System.setProperty("javax.net.ssl.trustStore", "");
        System.setProperty("javax.net.ssl.trustStorePassword", "");
        System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");
        
        // 测试SSL配置
        testSSLConfiguration();
        
        System.out.println();
        
        // 测试HTTPS连接
        testHttpsConnection();
        
        System.out.println("=== 测试完成 ===");
    }
}
