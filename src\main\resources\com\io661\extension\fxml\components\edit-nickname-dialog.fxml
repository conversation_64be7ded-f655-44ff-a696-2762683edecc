<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import java.net.URL?>
<AnchorPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="200.0"
            prefWidth="350.0"
            xmlns="http://javafx.com/javafx/21"
            fx:controller="com.io661.extension.controller.UserController">
    <stylesheets>
        <URL value="@../../css/edit-user-info-card.css" />
    </stylesheets>

    <VBox spacing="20" AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0" AnchorPane.bottomAnchor="20.0">
        <!-- 标题和关闭按钮 -->
        <HBox alignment="CENTER_LEFT" spacing="10">
            <Label text="修改昵称" styleClass="title-label" />
            <HBox HBox.hgrow="ALWAYS" />
            <Button fx:id="closeNicknameButton" text="×" styleClass="close-button" />
        </HBox>

        <!-- 输入提示 -->
        <Label text="请输入新昵称" styleClass="info-label" />

        <!-- 昵称输入框 -->
        <TextField fx:id="nicknameInputField" promptText="昵称长度不超过8个中文字符长度" />

        <!-- 提示文字 -->
        <Label text="昵称长度不超过8个中文字符长度，新的昵称提交后需等待审核完成后才会修改" styleClass="hint-label" wrapText="true" />

        <!-- 按钮区域 -->
        <HBox spacing="15" alignment="CENTER">
            <Button fx:id="cancelNicknameButton" text="返回" styleClass="cancel-button" prefWidth="80" />
            <Button fx:id="submitNicknameButton" text="提交" styleClass="submit-button" prefWidth="80" />
        </HBox>
    </VBox>
</AnchorPane>
