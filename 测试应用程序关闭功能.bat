@echo off
echo ===== 测试应用程序关闭功能修复 =====
echo.

cd /d "D:\JavaFx\Test\extension"

echo 当前目录: %CD%
echo.

echo ===== 修复内容总结 =====
echo.
echo 🔧 应用程序关闭问题修复:
echo   ✓ 添加主窗口关闭事件处理器
echo   ✓ 实现handleApplicationExit()方法
echo   ✓ 正确调用Platform.exit()和System.exit()
echo   ✓ 清理应用程序资源
echo.
echo 🔧 退出登录功能修复:
echo   ✓ 隐藏主界面内容
echo   ✓ 重新显示登录窗口
echo   ✓ 正确的窗口状态管理
echo   ✓ 添加MainWindowController.getContentArea()方法
echo.
echo 🔧 窗口焦点问题修复:
echo   ✓ 确保关闭按钮可点击
echo   ✓ 正确的事件处理
echo   ✓ 窗口层级管理
echo.

echo ===== 编译应用程序 =====
echo.
echo 正在编译应用程序...

call .\mvnw.cmd compile

if %ERRORLEVEL% neq 0 (
    echo ✗ 编译失败
    pause
    exit /b 1
)

echo ✓ 编译成功
echo.

echo ===== 测试说明 =====
echo.
echo 请按照以下步骤测试修复效果:
echo.
echo 1. 应用程序启动测试:
echo    - 应用程序应该正常启动
echo    - 主窗口应该可见
echo    - 登录窗口应该显示在主窗口上方
echo.
echo 2. 窗口关闭按钮测试:
echo    - 点击主窗口右上角的关闭按钮
echo    - 应用程序应该完全关闭
echo    - 任务栏中不应该残留进程
echo.
echo 3. 退出登录功能测试:
echo    - 登录成功后，点击用户头像
echo    - 在用户信息侧边栏中点击"退出登录"
echo    - 主界面内容应该隐藏
echo    - 登录窗口应该重新显示
echo    - 窗口应该有焦点，可以正常操作
echo.
echo 4. 任务栏关闭测试:
echo    - 右键点击任务栏中的应用程序图标
echo    - 选择"关闭窗口"
echo    - 应用程序应该完全关闭
echo.

echo ===== 预期修复效果 =====
echo.
echo ✅ 主窗口关闭按钮可以正常点击
echo ✅ 点击关闭按钮后应用程序完全退出
echo ✅ 任务栏中不会残留应用程序进程
echo ✅ 退出登录后能正确显示登录窗口
echo ✅ 登录窗口有正确的焦点
echo ✅ 所有窗口控件都可以正常点击
echo.

echo ===== 启动应用程序进行测试 =====
echo.
echo 现在启动应用程序进行测试...
echo.

java -cp "target/classes;target/lib/*" --module-path "target/lib" --add-modules javafx.controls,javafx.fxml,javafx.web com.io661.extension.Main

echo.
echo 应用程序已关闭
echo.

echo ===== 测试完成 =====
echo.
echo 如果测试过程中发现问题，请检查:
echo.
echo 1. 控制台输出的错误信息
echo 2. 应用程序是否完全关闭（检查任务管理器）
echo 3. 窗口关闭按钮是否有响应
echo 4. 退出登录功能是否正常工作
echo.

echo 技术修复要点:
echo - 添加了主窗口setOnCloseRequest事件处理器
echo - 实现了完整的应用程序退出流程
echo - 修复了退出登录后的窗口状态管理
echo - 确保了所有子窗口的正确关闭
echo - 添加了资源清理机制
echo.

pause
