/* 基础样式 */

AnchorPane {
    -fx-background-color: #ffffff;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 5, 0, 0, 1);
}


/* 标题样式 */

.title-label {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}


/* 信息标签样式 */

.info-label {
    -fx-font-size: 14px;
    -fx-text-fill: #666666;
    -fx-padding: 5 0;
    -fx-alignment: center-left;
}


/* 值标签样式 */

.value-label {
    -fx-font-size: 14px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
    -fx-padding: 5 0;
    -fx-alignment: center-left;
}


/* 操作按钮样式 */

.action-button {
    -fx-background-color: #1890ff;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 8 16;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.action-button:hover {
    -fx-background-color: #40a9ff;
}


/* 平铺效果的样式 */
.tiled-button {
    -fx-background-color: transparent;
    -fx-border-color: #1890ff;
    -fx-border-width: 1px;
    -fx-border-radius: 20px;
    -fx-padding: 8 16;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

/* 按钮悬停时的样式 */
.tiled-button:hover {
    -fx-background-color: #1890ff;
    -fx-background-radius: 20px;
    -fx-border-color: #1890ff;
    -fx-border-width: 1px;
    -fx-border-radius: 20px;
    -fx-padding: 8 16;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}


/* 文本输入框样式 */

TextField {
    -fx-background-color: #ffffff;
    -fx-border-color: #d9d9d9;
    -fx-border-radius: 4px;
    -fx-border-width: 1px;
    -fx-padding: 8 12;
    -fx-font-size: 14px;
}

TextField:focused {
    -fx-border-color: #1890ff;
    -fx-effect: dropshadow(gaussian, rgba(24, 144, 255, 0.2), 4, 0, 0, 0);
}


/* 关闭按钮样式 */

.close-button {
    -fx-background-color: #f5f5f5;
    -fx-text-fill: #f5222d;
    -fx-border-color: #d9d9d9;
    -fx-border-radius: 50%;
    -fx-border-width: 1px;
    -fx-padding: 5 15;
    -fx-font-size: 13px;
}

.close-button:hover {
    -fx-background-color: #fff2f0;
}


/* 提交按钮样式 */

.submit-button {
    -fx-background-color: #722ed1;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 8 16;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.submit-button:hover {
    -fx-background-color: #9254de;
}


/* 取消按钮样式 */

.cancel-button {
    -fx-background-color: #ffffff;
    -fx-text-fill: #666666;
    -fx-border-color: #d9d9d9;
    -fx-border-radius: 4px;
    -fx-border-width: 1px;
    -fx-padding: 8 16;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.cancel-button:hover {
    -fx-background-color: #f5f5f5;
    -fx-border-color: #40a9ff;
}


/* 提示文字样式 */

.hint-label {
    -fx-font-size: 12px;
    -fx-text-fill: #999999;
    -fx-padding: 5 0;
    -fx-wrap-text: true;
}


/* 头像预览样式 */

ImageView {
    -fx-background-radius: 50%;
    -fx-border-radius: 50%;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
}

.close-buttonx {
    -fx-font-size: 20px;
    -fx-background-color: transparent;
    -fx-text-fill: black;
}
.close-buttonx:hover {
    -fx-text-fill: #4C4A4AFF;
}