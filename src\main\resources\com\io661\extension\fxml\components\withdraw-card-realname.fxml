<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import java.net.URL?>
<VBox xmlns:fx="http://javafx.com/fxml"
      xmlns="http://javafx.com/javafx"
      fx:controller="com.io661.extension.controller.UserController"
      prefHeight="400.0" prefWidth="600.0"
      styleClass="realname-card">
    <stylesheets>
        <URL value="@../../css/withdraw-card-realname.css" />
    </stylesheets>
    <padding>
        <Insets top="20" right="20" bottom="20" left="20"/>
    </padding>

    <!-- 标题和关闭按钮 -->
    <HBox alignment="CENTER_LEFT" spacing="10">
        <Label styleClass="realname-title" text="实名认证" />
        <HBox alignment="TOP_RIGHT" HBox.hgrow="ALWAYS">
            <Button fx:id="closeButtonx" styleClass="close-button" text="×"/>
        </HBox>
    </HBox>

    <!-- 姓名输入框 -->
    <HBox styleClass="input-row">
        <Label styleClass="input-label" text="姓名："/>
        <TextField fx:id="withdrawName" promptText="请输入真实姓名" styleClass="realname-input" HBox.hgrow="ALWAYS"/>
    </HBox>

    <!-- 身份证号输入框 -->
    <HBox styleClass="input-row">
        <Label styleClass="input-label" text="身份证号："/>
        <TextField fx:id="withdrawCertNo" promptText="请输入18位身份证号" styleClass="realname-input" HBox.hgrow="ALWAYS"/>
    </HBox>

    <!-- 提示信息 -->
    <VBox styleClass="info-section">
        <Label styleClass="notice-text" text="🔒 安全提示"/>
        <Label styleClass="info-text" text="• 仅首次提现需要身份验证"/>
        <Label styleClass="info-text" text="• 您的个人信息将被严格保护"/>
        <Label styleClass="info-text" text="• 请确保信息真实有效"/>
    </VBox>

    <!-- 确定按钮 -->
    <HBox styleClass="button-row">
        <Button fx:id="submitRealName" styleClass="submit-button" text="确定"/>
    </HBox>
</VBox>