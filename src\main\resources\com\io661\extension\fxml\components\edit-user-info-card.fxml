<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import java.net.URL?>
<AnchorPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="300.0"
            xmlns="http://javafx.com/javafx/21"
            prefWidth="450.0"
            fx:controller="com.io661.extension.controller.UserController">
    <stylesheets>
        <URL value="@../../css/edit-user-info-card.css" />
    </stylesheets>

    <VBox spacing="15" AnchorPane.bottomAnchor="15.0" AnchorPane.leftAnchor="15.0" AnchorPane.rightAnchor="15.0" AnchorPane.topAnchor="15.0">
        <!-- 标题 -->
        <HBox alignment="CENTER_LEFT" spacing="10">
            <Label styleClass="title-label" text="个人资料" />
            <!-- 关闭按钮 -->
            <HBox alignment="TOP_RIGHT" spacing="10" />
        </HBox>



        <!-- 使用GridPane实现3列4行布局 -->
        <GridPane alignment="CENTER" hgap="15" vgap="15">
            <columnConstraints>
                <ColumnConstraints minWidth="100" prefWidth="100" />
                <ColumnConstraints minWidth="200" prefWidth="200" />
                <ColumnConstraints minWidth="100" prefWidth="100" />
            </columnConstraints>
            <rowConstraints>
                <RowConstraints minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
            <RowConstraints />
            </rowConstraints>

            <!-- 第一行：头像 -->
            <Label styleClass="info-label" text="头像" GridPane.columnIndex="0" GridPane.halignment="CENTER" GridPane.rowIndex="0" GridPane.valignment="CENTER" />
            <ImageView fx:id="profileImage" fitHeight="40.0" fitWidth="40.0" preserveRatio="true" GridPane.columnIndex="1" GridPane.halignment="CENTER" GridPane.rowIndex="0" GridPane.valignment="CENTER" />
            <Button fx:id="editAvatar" prefWidth="90" styleClass="action-button" text="修改头像" GridPane.columnIndex="2" GridPane.halignment="CENTER" GridPane.rowIndex="0" GridPane.valignment="CENTER" />

            <!-- 第二行：昵称 -->
            <Label styleClass="info-label" text="昵称" GridPane.columnIndex="0" GridPane.halignment="CENTER" GridPane.rowIndex="1" GridPane.valignment="CENTER" />
            <Label fx:id="nicknameLabel" styleClass="value-label" text="用户昵称" GridPane.columnIndex="1" GridPane.halignment="CENTER" GridPane.rowIndex="1" GridPane.valignment="CENTER" />
            <Button fx:id="editNickName" prefWidth="90" styleClass="action-button" text="修改昵称" GridPane.columnIndex="2" GridPane.halignment="CENTER" GridPane.rowIndex="1" GridPane.valignment="CENTER" />

            <!-- 第三行：UID -->
            <Label styleClass="info-label" text="UID" GridPane.columnIndex="0" GridPane.halignment="CENTER" GridPane.rowIndex="2" GridPane.valignment="CENTER" />
            <Label fx:id="uidLabel" styleClass="value-label" text="用户ID" GridPane.columnIndex="1" GridPane.halignment="CENTER" GridPane.rowIndex="2" GridPane.valignment="CENTER" />

            <!-- 第四行：绑定手机号 -->
            <Label styleClass="info-label" text="绑定手机号" GridPane.columnIndex="0" GridPane.halignment="CENTER" GridPane.rowIndex="3" GridPane.valignment="CENTER" />
            <Label fx:id="phoneLabel" styleClass="value-label" text="手机号" GridPane.columnIndex="1" GridPane.halignment="CENTER" GridPane.rowIndex="3" GridPane.valignment="CENTER" />

            <!-- 实名状态区域 -->
            <Label styleClass="info-label" text="实名状态" GridPane.columnIndex="0" GridPane.halignment="CENTER" GridPane.rowIndex="4" GridPane.valignment="CENTER" />
            <Label fx:id="realNameStatusLabel" styleClass="value-label" text="未实名" GridPane.columnIndex="1" GridPane.halignment="CENTER" GridPane.rowIndex="4" GridPane.valignment="CENTER" />

        </GridPane>

        <!-- 关闭按钮 -->
        <HBox alignment="CENTER" spacing="10">
            <Button fx:id="closeButton" onAction="#handleCloseDialog" prefWidth="400" styleClass="tiled-button" text="关闭" />
        </HBox>
    </VBox>
       <Button fx:id="closeButtonx" layoutX="438.0" layoutY="7.0" onAction="#closeWindow" styleClass="close-buttonx" text="×" />
</AnchorPane>
