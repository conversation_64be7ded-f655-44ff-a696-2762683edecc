<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import java.net.URL?>
<AnchorPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="400.0" prefWidth="568.0"
            styleClass="withdraw-card"
            xmlns="http://javafx.com/javafx/21"
            fx:controller="com.io661.extension.controller.UserController">
    <stylesheets>
        <URL value="@../../css/withdraw-card.css" />
    </stylesheets>
    <VBox layoutX="1.0" layoutY="44.0" prefHeight="361.0" prefWidth="599.0" styleClass="withdraw-container" AnchorPane.bottomAnchor="-5.0" AnchorPane.leftAnchor="1.0" AnchorPane.rightAnchor="-1.0" AnchorPane.topAnchor="44.0">
        <padding>
            <Insets bottom="20" left="20" right="20" top="20" />
        </padding>

        <!-- 可提现金额 -->
        <HBox styleClass="info-row">
            <Label styleClass="available-amount-label" text="可提现：" />
            <Label fx:id="availableWithdrawAmount" styleClass="available-amount-value" text="¥0.00" />
        </HBox>

        <!-- 提现金额 -->
        <HBox styleClass="info-row">
            <Label styleClass="info-label" text="提现金额:" />
            <TextField fx:id="withdrawAmount" prefWidth="200" promptText="请输入提现金额" styleClass="withdraw-input" />
        </HBox>

        <!-- 实际到账和手续费 -->
        <HBox styleClass="amount-row">
            <HBox styleClass="info-row">
                <Label styleClass="info-label" text="实际到账：" />
                <Label fx:id="actualAmount" styleClass="value-label" text="¥0" />
            </HBox>
            <HBox styleClass="info-row">
                <Label styleClass="info-label" text="手续费：" />
                <Label fx:id="feeAmount" styleClass="fee-label" text="¥0" />
            </HBox>
        </HBox>

        <!-- 支付宝账号 -->
        <HBox styleClass="info-row">
            <Label styleClass="info-label" text="支付宝账号(手机号):" />
            <TextField fx:id="withdrawAccountDisplay" editable="false" styleClass="account-display" text="" />
        </HBox>

        <!-- 提现须知 -->
        <VBox spacing="5">
            <Label styleClass="notice-title" text="提现须知" />
            <Label styleClass="notice-item" text="1. 只允许提现到实名认证所绑定的支付宝" />
            <Label styleClass="notice-item" text="2. 发起提现后预计一个工作日内处理完毕" />
            <Label styleClass="notice-item" text="3. 每笔提现最低10元，上不封顶" />
        </VBox>

        <!-- 确定提现按钮 -->
        <Button fx:id="startWithdrawButton" styleClass="withdraw-button" text="确定提现" />
    </VBox>
    <Button fx:id="closeButtonx" layoutX="574.0" layoutY="3.0" styleClass="close-buttonx" text="×" />
    <Label layoutX="14.0" layoutY="14.0" styleClass="withdraw-title" text="提现" />
</AnchorPane>
