/* 提现卡片样式 */
.withdraw-card {
    -fx-background-color: linear-gradient(to bottom, #2c2c2c, #1e1e1e);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 15, 0, 0, 3);
    -fx-border-color: #404040;
    -fx-border-width: 1px;
}

/* 标题样式 */
.withdraw-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #ffffff;
    -fx-padding: 0 0 10 0;
}

/* 关闭按钮样式 */
.close-buttonx {
    -fx-background-color: transparent;
    -fx-text-fill: #ffffff;
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-background-radius: 50%;
    -fx-min-width: 30px;
    -fx-min-height: 30px;
    -fx-max-width: 30px;
    -fx-max-height: 30px;
}

.close-buttonx:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-text-fill: #ff6b6b;
}

.close-buttonx:pressed {
    -fx-background-color: rgba(255, 255, 255, 0.2);
}

/* 可提现金额样式 */
.available-amount-label {
    -fx-text-fill: #ffffff;
    -fx-font-size: 16px;
    -fx-font-weight: normal;
}

.available-amount-value {
    -fx-text-fill: #4CAF50;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
}

/* 输入框样式 */
.withdraw-input {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: #ffffff;
    -fx-font-size: 14px;
    -fx-background-radius: 6px;
    -fx-border-color: #555555;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-prompt-text-fill: #888888;
}

.withdraw-input:focused {
    -fx-border-color: #00CED1;
    -fx-border-width: 2px;
    -fx-background-color: #404040;
}

/* 标签样式 */
.info-label {
    -fx-text-fill: #ffffff;
    -fx-font-size: 14px;
    -fx-font-weight: normal;
}

.value-label {
    -fx-text-fill: #4CAF50;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.fee-label {
    -fx-text-fill: #FFA726;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

/* 账号显示样式 */
.account-display {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: #ffffff;
    -fx-font-size: 14px;
    -fx-background-radius: 6px;
    -fx-border-color: #555555;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-opacity: 0.8;
}

/* 提现须知样式 */
.notice-title {
    -fx-text-fill: #ffffff;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-padding: 10 0 5 0;
}

.notice-item {
    -fx-text-fill: #cccccc;
    -fx-font-size: 12px;
    -fx-padding: 2 0;
}

/* 确定提现按钮样式 */
.withdraw-button {
    -fx-background-color: linear-gradient(to bottom, #00CED1, #00B8CC);
    -fx-text-fill: #ffffff;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-cursor: hand;
    -fx-padding: 12px 30px;
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.3), 8, 0, 0, 2);
}

.withdraw-button:hover {
    -fx-background-color: linear-gradient(to bottom, #00E5E8, #00CED1);
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.5), 12, 0, 0, 3);
}

.withdraw-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #00B8CC, #00A5B8);
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.2), 4, 0, 0, 1);
}

.withdraw-button:disabled {
    -fx-background-color: #666666;
    -fx-text-fill: #999999;
    -fx-effect: none;
    -fx-cursor: default;
}

/* 容器样式 */
.withdraw-container {
    -fx-spacing: 15px;
    -fx-padding: 20px;
}

.info-row {
    -fx-spacing: 10px;
    -fx-alignment: CENTER_LEFT;
}

.amount-row {
    -fx-spacing: 20px;
    -fx-alignment: CENTER_LEFT;
}

/* 动画效果 */
.withdraw-card {
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
}